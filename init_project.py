#!/usr/bin/env python3
"""
PUF Authentication System - 项目初始化脚本
自动创建必要的目录结构和配置文件
"""

import os
import sys

def create_directories():
    """创建必要的目录结构"""
    directories = [
        'results',
        'results/trained_models',
        'results/sample_images',
        'results/training_logs',
        'results/preview_samples'
    ]
    
    print("创建目录结构...")
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
            
            # 创建.gitkeep文件以确保空目录被git跟踪
            gitkeep_path = os.path.join(directory, '.gitkeep')
            with open(gitkeep_path, 'w') as f:
                f.write('# 此文件确保空目录被git跟踪\n')
        else:
            print(f"📁 目录已存在: {directory}")

def check_dependencies():
    """检查依赖库是否安装"""
    print("\n检查依赖库...")
    
    required_packages = [
        ('torch', 'PyTorch深度学习框架'),
        ('torchvision', 'PyTorch计算机视觉库'),
        ('PIL', 'Python图像处理库'),
        ('numpy', '数值计算库'),
        ('matplotlib', '绘图库'),
        ('tqdm', '进度条库'),
        ('psutil', '系统信息库'),
        ('tkinter', 'GUI框架')
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖库: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖库已安装!")
        return True

def test_gpu():
    """测试GPU可用性"""
    print("\n检查GPU支持...")
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ 检测到 {gpu_count} 个GPU: {gpu_name}")
            
            # 简单的GPU测试
            try:
                x = torch.randn(100, 100, device='cuda')
                y = torch.mm(x, x)
                del x, y
                torch.cuda.empty_cache()
                print("✅ GPU测试通过")
            except Exception as e:
                print(f"⚠️  GPU测试失败: {e}")
        else:
            print("💻 未检测到CUDA GPU，将使用CPU模式")
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU")

def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
\"\"\"
PUF Authentication System - 快速测试脚本
验证程序是否能正常启动
\"\"\"

def test_imports():
    \"\"\"测试模块导入\"\"\"
    try:
        print("测试核心模块导入...")
        from puf_core import get_optimal_workers, ensure_user_directories
        print("✅ 核心模块导入成功")
        
        print("测试GUI模块导入...")
        from puf_gui import PUFAuthenticationApp
        print("✅ GUI模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_basic_functions():
    \"\"\"测试基本功能\"\"\"
    try:
        from puf_core import get_optimal_workers, ensure_user_directories
        
        workers = get_optimal_workers()
        print(f"✅ 最优工作线程数: {workers}")
        
        dirs = ensure_user_directories()
        print(f"✅ 目录创建成功: {len(dirs)} 个目录")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("PUF Authentication System - 快速测试")
    print("=" * 50)
    
    success = True
    success &= test_imports()
    success &= test_basic_functions()
    
    if success:
        print("\\n🎉 所有测试通过！程序可以正常运行。")
        print("运行 'python main.py' 启动程序")
    else:
        print("\\n❌ 测试失败，请检查依赖安装")
    
    print("=" * 50)
"""
    
    with open('test_quick.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    print("✅ 创建测试脚本: test_quick.py")

def main():
    """主函数"""
    print("=" * 60)
    print("PUF Authentication System - 项目初始化")
    print("=" * 60)
    
    # 创建目录结构
    create_directories()
    
    # 创建测试脚本
    create_test_script()
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    # 测试GPU
    if deps_ok:
        test_gpu()
    
    print("\n" + "=" * 60)
    print("项目初始化完成!")
    print("=" * 60)
    
    if deps_ok:
        print("\n✅ 环境检查通过，可以开始使用!")
        print("\n使用方法:")
        print("1. 运行程序: python main.py")
        print("2. 快速测试: python test_quick.py")
        print("3. 打包程序: pyinstaller PUF_Auth.spec")
    else:
        print("\n⚠️  请先安装依赖库:")
        print("pip install -r requirements.txt")
    
    print("\n📖 详细说明请查看 README.md")

if __name__ == "__main__":
    main()
