# PUF Authentication System - 依赖库列表
# 
# 安装方法：
# pip install -r requirements.txt
#
# 或者使用国内镜像源加速安装：
# pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 深度学习框架
torch>=2.0.0
torchvision>=0.15.0

# 图像处理
Pillow>=9.0.0
opencv-python>=4.5.0

# 数据处理和科学计算
numpy>=1.21.0
matplotlib>=3.5.0

# 进度条
tqdm>=4.64.0

# 系统信息
psutil>=5.8.0

# GUI框架 (通常Python自带，但某些环境可能需要)
# tkinter 通常是Python标准库的一部分，无需单独安装

# 可选：CUDA支持（如果使用GPU训练）
# 注意：torch和torchvision的CUDA版本需要根据您的CUDA版本选择
# 请访问 https://pytorch.org/get-started/locally/ 获取适合您系统的安装命令

# 开发和打包工具（可选）
# pyinstaller>=5.0  # 用于打包成可执行文件
# pytest>=7.0.0     # 用于单元测试

# 版本说明：
# - torch: 深度学习框架，支持GPU加速
# - torchvision: 计算机视觉工具包，包含预训练模型
# - Pillow: Python图像处理库
# - opencv-python: 计算机视觉库（可选，用于高级图像处理）
# - numpy: 数值计算库
# - matplotlib: 绘图库，用于显示训练曲线
# - tqdm: 进度条库
# - psutil: 系统和进程工具库
