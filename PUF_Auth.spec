# -*- mode: python ; coding: utf-8 -*-
"""
PUF Authentication System - PyInstaller配置文件
用于将Python程序打包成独立的可执行文件

使用方法：
1. 确保已安装PyInstaller: pip install pyinstaller
2. 在项目目录下运行: pyinstaller PUF_Auth.spec
3. 生成的可执行文件位于 dist/PUFAuth/ 目录下

注意事项：
- 确保所有依赖库已正确安装
- 打包过程可能需要较长时间，特别是包含PyTorch时
- 生成的文件较大，因为包含了完整的PyTorch库
"""

import sys
import os
from PyInstaller.utils.hooks import collect_all, collect_submodules

# 图标文件路径
icon_path = 'icon.ico'
if not os.path.exists(icon_path):
    print(f"警告: 图标文件 {icon_path} 不存在，将使用默认图标")
    icon_path = None

block_cipher = None

# 收集PyTorch和相关库的依赖
print("正在收集PyTorch依赖...")
torch_hidden_imports = collect_submodules('torch')
torchvision_hidden_imports = collect_submodules('torchvision')

# 合并所有隐藏导入
all_hidden_imports = torch_hidden_imports + torchvision_hidden_imports + [
    # PIL相关
    'PIL._tkinter_finder',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageEnhance',
    
    # PyTorch相关
    'torchvision.ops',
    'torch.distributions',
    'torch.distributions.constraints',
    'torch.distributions.normal',
    'torchvision.transforms.functional',
    'torch.nn.modules.activation',
    'torch.nn.modules.container',
    'torch.nn.modules.pooling',
    'torch.nn.modules.dropout',
    'torch.nn.modules.linear',
    'torch.nn.modules.batchnorm',
    
    # Matplotlib相关
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends._backend_tkagg',
    
    # 其他依赖
    'numpy',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'tkinter.ttk',
    'tqdm',
    'psutil',
    'multiprocessing',
    'threading',
    'concurrent.futures',
]

# 收集所有相关文件
print("正在收集相关文件...")
bins = []
datas = []
zips = []

# 收集PyTorch和torchvision的文件
for package in ['torch', 'torchvision']:
    try:
        package_bins, package_datas, package_zips = collect_all(package)
        bins.extend(package_bins)
        datas.extend(package_datas)
        zips.extend(package_zips)
        print(f"已收集 {package} 的文件")
    except Exception as e:
        print(f"警告: 收集 {package} 文件时出错: {e}")

# 创建必要的目录结构
result_dirs = [
    'results',
    'results/trained_models',
    'results/sample_images', 
    'results/training_logs',
    'results/preview_samples'
]

for subfolder in result_dirs:
    if not os.path.exists(subfolder):
        os.makedirs(subfolder)
        print(f"创建目录: {subfolder}")
    
    # 创建占位符文件以确保目录被包含
    placeholder_file = os.path.join(subfolder, '.placeholder')
    if not os.path.exists(placeholder_file):
        with open(placeholder_file, 'w', encoding='utf-8') as f:
            f.write('# 目录占位符文件\n# 此文件确保目录在打包时被包含\n')
    datas.append((placeholder_file, subfolder))

# 添加图标文件（如果存在）
if icon_path and os.path.exists(icon_path):
    datas.append((icon_path, '.'))

print("开始分析...")
a = Analysis(
    ['main.py'],  # 主程序入口
    pathex=[],
    binaries=bins,
    datas=datas,
    hiddenimports=all_hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'test',
        'tests',
        'testing',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

print("创建PYZ...")
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

print("创建EXE...")
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='PUFAuth',  # 可执行文件名
    debug=False,     # 设置为True可以看到更多调试信息
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,        # 使用UPX压缩（如果可用）
    console=False,   # 设置为False隐藏控制台窗口
    icon=icon_path,  # 程序图标
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

print("收集文件...")
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PUFAuth',  # 输出目录名
)

print("PyInstaller配置完成！")
print("使用方法: pyinstaller PUF_Auth.spec")
