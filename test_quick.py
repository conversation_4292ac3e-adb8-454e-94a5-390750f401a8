#!/usr/bin/env python3
"""
PUF Authentication System - 快速测试脚本
验证程序是否能正常启动
"""

def test_imports():
    """测试模块导入"""
    try:
        print("测试核心模块导入...")
        from puf_core import get_optimal_workers, ensure_user_directories
        print("✅ 核心模块导入成功")
        
        print("测试GUI模块导入...")
        from puf_gui import PUFAuthenticationApp
        print("✅ GUI模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_basic_functions():
    """测试基本功能"""
    try:
        from puf_core import get_optimal_workers, ensure_user_directories
        
        workers = get_optimal_workers()
        print(f"✅ 最优工作线程数: {workers}")
        
        dirs = ensure_user_directories()
        print(f"✅ 目录创建成功: {len(dirs)} 个目录")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("PUF Authentication System - 快速测试")
    print("=" * 50)
    
    success = True
    success &= test_imports()
    success &= test_basic_functions()
    
    if success:
        print("\n🎉 所有测试通过！程序可以正常运行。")
        print("运行 'python main.py' 启动程序")
    else:
        print("\n❌ 测试失败，请检查依赖安装")
    
    print("=" * 50)
