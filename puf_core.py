"""
PUF Authentication System - 核心训练模块
包含神经网络模型定义、数据处理、训练逻辑、验证功能等核心部分
"""

import os
import random
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image, ImageDraw, ImageEnhance
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import psutil

def get_optimal_workers():
    """获取最优的工作线程数"""
    cpu_count = multiprocessing.cpu_count()
    return min(cpu_count, 8)

def get_user_data_path():
    """获取用户数据目录路径 - 使用当前目录"""
    return os.path.abspath(".")

def ensure_user_directories():
    """确保用户数据目录存在"""
    user_data_dir = get_user_data_path()

    directories = {
        'RESULTS_DIR': os.path.join(user_data_dir, 'results'),
        'MODELS_DIR': os.path.join(user_data_dir, 'results', 'trained_models'),
        'SAMPLES_DIR': os.path.join(user_data_dir, 'results', 'sample_images'),
        'TRAINING_LOGS_DIR': os.path.join(user_data_dir, 'results', 'training_logs'),
        'PREVIEW_SAMPLES_DIR': os.path.join(user_data_dir, 'results', 'preview_samples')
    }

    for dir_name, dir_path in directories.items():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

    return directories

# 初始化目录
DIRS = ensure_user_directories()
RESULTS_DIR = DIRS['RESULTS_DIR']
MODELS_DIR = DIRS['MODELS_DIR']
SAMPLES_DIR = DIRS['SAMPLES_DIR']
TRAINING_LOGS_DIR = DIRS['TRAINING_LOGS_DIR']
PREVIEW_SAMPLES_DIR = DIRS['PREVIEW_SAMPLES_DIR']

def check_gpu_status():
    """简洁的GPU状态检查"""
    print("=" * 50)
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🚀 GPU: {device_name} ({gpu_memory:.1f}GB)")

        # 简单测试
        try:
            x = torch.randn(100, 100, device='cuda')
            y = torch.mm(x, x)
            del x, y
            torch.cuda.empty_cache()
            print("✅ GPU测试通过")
        except Exception as e:
            print(f"❌ GPU测试失败: {e}")
    else:
        print("💻 使用CPU模式")

    print(f"🔧 CPU核心: {multiprocessing.cpu_count()}")
    print(f"💾 可用内存: {psutil.virtual_memory().available / 1024**3:.1f}GB")
    print("=" * 50)

def set_seed(seed=42):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def preprocess_image_cpu(image_path, target_size=512):
    """CPU优化的图像预处理函数"""
    try:
        img = Image.open(image_path)
        original_width, original_height = img.size
        crop_dim = min(original_width, original_height)
        left = (original_width - crop_dim) // 2
        top = (original_height - crop_dim) // 2
        right = left + crop_dim
        bottom = top + crop_dim
        cropped_img = img.crop((left, top, right, bottom))

        if cropped_img.mode == 'RGBA':
            background = Image.new("RGB", cropped_img.size, (255, 255, 255))
            background.paste(cropped_img, mask=cropped_img.split()[3])
            cropped_img = background
        elif cropped_img.mode != 'RGB':
            cropped_img = cropped_img.convert('RGB')

        # 创建圆形蒙版
        mask = Image.new('L', cropped_img.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((1, 1, cropped_img.size[0] - 1, cropped_img.size[1] - 1), fill=255)

        masked_img = Image.new('RGB', cropped_img.size, (0, 0, 0))
        masked_img.paste(cropped_img, (0, 0), mask=mask)

        resized_img = masked_img.resize((target_size, target_size), Image.Resampling.LANCZOS)
        return resized_img
    except Exception as e:
        print(f"❌ 预处理图像失败 {image_path}: {e}")
        error_img = Image.new('RGB', (target_size, target_size), (0, 0, 0))
        draw = ImageDraw.Draw(error_img)
        draw.line([(10, 10), (target_size - 10, target_size - 10)], fill=(255, 0, 0), width=5)
        draw.line([(10, target_size - 10), (target_size - 10, 10)], fill=(255, 0, 0), width=5)
        return error_img

def preprocess_image(image_path, target_size=512):
    """保持向后兼容的预处理函数"""
    return preprocess_image_cpu(image_path, target_size)

class PUFDataset(Dataset):
    """优化的数据集类"""
    def __init__(self, image_paths, is_positive, base_transform=None, augmentations_per_image=500,
                 save_sample_count=5, use_cpu_preprocessing=True):
        self.image_paths = image_paths
        self.is_positive = is_positive
        self.label = 1 if is_positive else 0
        self.augmentations_per_image = augmentations_per_image
        self.base_transform = base_transform
        self.save_sample_count = save_sample_count
        self.use_cpu_preprocessing = use_cpu_preprocessing

        # 优化的数据增强
        self.pil_augment_transform = transforms.Compose([
            transforms.RandomAffine(degrees=(-180, 180), translate=(0.15, 0.15),
                                  scale=(0.85, 1.15), shear=(-10, 10, -10, 10), fill=0),
            transforms.RandomPerspective(distortion_scale=0.2, p=0.4, fill=0),
            transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.2, hue=0.1),
            transforms.RandomApply([transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.0))], p=0.3),
        ])

        # 预处理图像
        label_type = "正样本" if is_positive else "负样本"
        print(f"📸 预处理{label_type}图像...")
        self.original_images = []

        if use_cpu_preprocessing and len(image_paths) > 1:
            with ProcessPoolExecutor(max_workers=get_optimal_workers()) as executor:
                self.original_images = list(executor.map(preprocess_image_cpu, image_paths))
        else:
            for path in image_paths:
                self.original_images.append(preprocess_image_cpu(path))

        # 保存样本
        if image_paths:
            img_type = "positive" if is_positive else "negative"
            save_path = os.path.join(SAMPLES_DIR, f"dataset_input_{img_type}_{os.path.basename(image_paths[0])}.png")
            self.original_images[0].save(save_path)

    def __len__(self):
        return len(self.original_images) * self.augmentations_per_image

    def __getitem__(self, idx):
        original_image_idx = (idx // self.augmentations_per_image) % len(self.original_images)
        augmentation_iteration = idx % self.augmentations_per_image

        original_pil_image = self.original_images[original_image_idx]
        augmented_pil_image = self.pil_augment_transform(original_pil_image)

        # 保存增强样本
        if original_image_idx == 0 and augmentation_iteration < self.save_sample_count:
            img_type = "positive" if self.is_positive else "negative"
            orig_filename_stem = os.path.splitext(os.path.basename(self.image_paths[original_image_idx]))[0]
            sample_save_path = os.path.join(SAMPLES_DIR, f"aug_{img_type}_{orig_filename_stem}_{augmentation_iteration}.png")
            try:
                augmented_pil_image.save(sample_save_path)
            except Exception as e:
                print(f"❌ 保存增强样本失败: {e}")

        tensor_image = augmented_pil_image
        if self.base_transform:
            tensor_image = self.base_transform(tensor_image)

        return tensor_image, self.label

def create_model(dropout_rate=0.3, precision='double'):
    """创建优化的AlexNet模型"""
    print(f"🔧 创建AlexNet模型 ({precision} precision)...")

    model = models.alexnet(weights=None)

    for param in model.parameters():
        param.requires_grad = True

    # 简化的分类器
    original_classifier_input = model.classifier[1].in_features if len(model.classifier) > 1 else 256 * 6 * 6

    model.classifier = nn.Sequential(
        nn.Dropout(dropout_rate),
        nn.Linear(original_classifier_input, 512),
        nn.ReLU(inplace=True),
        nn.BatchNorm1d(512),
        nn.Dropout(dropout_rate * 0.7),
        nn.Linear(512, 128),
        nn.ReLU(inplace=True),
        nn.BatchNorm1d(128),
        nn.Dropout(dropout_rate * 0.5),
        nn.Linear(128, 1)
    )

    # 权重初始化
    for m in model.classifier.modules():
        if isinstance(m, nn.Linear):
            nn.init.xavier_uniform_(m.weight)
            nn.init.constant_(m.bias, 0)

    # 设置精度
    if precision == 'double':
        model = model.double()
    elif precision == 'float':
        model = model.float()
    elif precision == 'half':
        model = model.half()

    print(f"✅ 模型创建完成，分类器输入特征: {original_classifier_input} ({precision} precision)")
    return model

class GPUMemoryManager:
    """GPU内存管理"""
    def __init__(self, device):
        self.device = device
        self.is_cuda = device.type == 'cuda'

    def clear_cache(self):
        if self.is_cuda:
            torch.cuda.empty_cache()

    def get_memory_info(self):
        if self.is_cuda:
            allocated = torch.cuda.memory_allocated(self.device) / 1024**3
            cached = torch.cuda.memory_reserved(self.device) / 1024**3
            return f"{allocated:.1f}GB/{cached:.1f}GB"
        return "CPU"

    def optimize_for_training(self):
        if self.is_cuda:
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True

# === GPU训练器 ===
class GPUTrainer:
    """专门的GPU训练器"""
    def __init__(self, model, device, precision='double'):
        self.device = device
        self.precision = precision
        self.gpu_manager = GPUMemoryManager(device)
        self.gpu_manager.optimize_for_training()

        # 设置模型精度
        if precision == 'double':
            self.model = model.to(device).double()
            self.dtype = torch.float64
        elif precision == 'half':
            self.model = model.to(device).half()
            self.dtype = torch.float16
            self.scaler = torch.cuda.amp.GradScaler()
            self.use_amp = True
        else:  # float
            self.model = model.to(device).float()
            self.dtype = torch.float32
            self.scaler = torch.cuda.amp.GradScaler()
            self.use_amp = True

        print(f"🚀 GPU训练器初始化完成 ({precision} precision)")

    def train_epoch(self, train_loader, criterion, optimizer):
        self.model.train()
        running_loss, correct_preds, total_samples = 0.0, 0, 0

        for batch_idx, (inputs, labels) in enumerate(train_loader):
            inputs = inputs.to(self.device, non_blocking=True).to(self.dtype)
            labels = labels.to(self.device, non_blocking=True).to(self.dtype).unsqueeze(1)

            optimizer.zero_grad()

            if hasattr(self, 'use_amp') and self.use_amp:
                with torch.cuda.amp.autocast():
                    outputs = self.model(inputs)
                    loss = criterion(outputs, labels)
                self.scaler.scale(loss).backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.scaler.step(optimizer)
                self.scaler.update()
            else:
                outputs = self.model(inputs)
                loss = criterion(outputs, labels)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()

            # 统计
            running_loss += loss.item() * inputs.size(0)
            with torch.no_grad():
                predicted = (torch.sigmoid(outputs) > 0.5).to(self.dtype)
                total_samples += labels.size(0)
                correct_preds += (predicted == labels).sum().item()

            # 定期清理内存
            if batch_idx % 50 == 0:
                self.gpu_manager.clear_cache()

        return running_loss / total_samples, correct_preds / total_samples

    def validate_epoch(self, val_loader, criterion):
        self.model.eval()
        running_loss, correct_preds, total_samples = 0.0, 0, 0

        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs = inputs.to(self.device, non_blocking=True).to(self.dtype)
                labels = labels.to(self.device, non_blocking=True).to(self.dtype).unsqueeze(1)

                if hasattr(self, 'use_amp') and self.use_amp:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(inputs)
                        loss = criterion(outputs, labels)
                else:
                    outputs = self.model(inputs)
                    loss = criterion(outputs, labels)

                running_loss += loss.item() * inputs.size(0)
                predicted = (torch.sigmoid(outputs) > 0.5).to(self.dtype)
                total_samples += labels.size(0)
                correct_preds += (predicted == labels).sum().item()

        return running_loss / total_samples, correct_preds / total_samples

# === CPU训练器 ===
class CPUTrainer:
    """专门的CPU训练器"""
    def __init__(self, model, device, precision='double'):
        self.device = device
        self.precision = precision

        # 设置模型精度
        if precision == 'double':
            self.model = model.to(device).double()
            self.dtype = torch.float64
        else:  # float
            self.model = model.to(device).float()
            self.dtype = torch.float32

        print(f"💻 CPU训练器初始化完成 ({precision} precision)")

    def train_epoch(self, train_loader, criterion, optimizer):
        self.model.train()
        running_loss, correct_preds, total_samples = 0.0, 0, 0

        for inputs, labels in train_loader:
            inputs = inputs.to(self.device).to(self.dtype)
            labels = labels.to(self.device).to(self.dtype).unsqueeze(1)

            optimizer.zero_grad()
            outputs = self.model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()

            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            optimizer.step()

            # 统计
            running_loss += loss.item() * inputs.size(0)
            with torch.no_grad():
                predicted = (torch.sigmoid(outputs) > 0.5).to(self.dtype)
                total_samples += labels.size(0)
                correct_preds += (predicted == labels).sum().item()

        return running_loss / total_samples, correct_preds / total_samples

    def validate_epoch(self, val_loader, criterion):
        self.model.eval()
        running_loss, correct_preds, total_samples = 0.0, 0, 0

        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs = inputs.to(self.device).to(self.dtype)
                labels = labels.to(self.device).to(self.dtype).unsqueeze(1)

                outputs = self.model(inputs)
                loss = criterion(outputs, labels)

                running_loss += loss.item() * inputs.size(0)
                predicted = (torch.sigmoid(outputs) > 0.5).to(self.dtype)
                total_samples += labels.size(0)
                correct_preds += (predicted == labels).sum().item()

        return running_loss / total_samples, correct_preds / total_samples

def train_model_optimized(trainer, train_loader, val_loader, criterion, optimizer, scheduler, num_epochs, device, model_version, save_dir, app=None, early_stop_patience=12):
    """优化的训练函数 - 添加早停机制控制"""
    best_val_acc = 0.0
    train_losses, train_accs = [], []
    val_losses, val_accs = [], []

    # 早停机制
    patience_counter = 0
    best_loss = float('inf')

    best_model_path = os.path.join(save_dir, f"puf_{model_version}_best.pth")
    final_model_path = os.path.join(save_dir, f"puf_{model_version}_final.pth")

    print(f"💾 模型保存路径: {save_dir}")
    print(f"📁 模型前缀: puf_{model_version}")
    print(f"⏰ 早停耐心值: {early_stop_patience}")

    for epoch in range(num_epochs):
        # 检查强制停止
        if app and hasattr(app, 'force_stop_training') and app.force_stop_training:
            print(f"⏹️ 在第 {epoch + 1} 轮被强制停止")
            break

        print(f"\n📅 第 {epoch + 1}/{num_epochs} 轮")

        # 训练阶段
        train_loss, train_acc = trainer.train_epoch(train_loader, criterion, optimizer)
        train_losses.append(train_loss)
        train_accs.append(train_acc)

        # 验证阶段
        val_loss, val_acc = None, None
        if val_loader:
            val_loss, val_acc = trainer.validate_epoch(val_loader, criterion)
            val_losses.append(val_loss)
            val_accs.append(val_acc)

            # 学习率调度
            if scheduler:
                if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_loss)
                else:
                    scheduler.step()

            current_lr = optimizer.param_groups[0]['lr']
            print(f"📊 训练 - 损失: {train_loss:.4f}, 准确率: {train_acc:.4f}")
            print(f"📊 验证 - 损失: {val_loss:.4f}, 准确率: {val_acc:.4f}")
            print(f"📈 学习率: {current_lr:.6f}")

            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                torch.save(trainer.model.state_dict(), best_model_path)
                print(f"💾 最佳模型已保存: {os.path.basename(best_model_path)} (准确率: {best_val_acc:.4f})")
                patience_counter = 0
            else:
                patience_counter += 1

            # 早停检查
            if patience_counter >= early_stop_patience:
                print(f"⏹️ 早停触发: 验证准确率在 {early_stop_patience} 轮内无改善")
                break

        else:
            # 无验证集
            if scheduler and not isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step()

            current_lr = optimizer.param_groups[0]['lr']
            print(f"📊 训练 - 损失: {train_loss:.4f}, 准确率: {train_acc:.4f}")
            print(f"📈 学习率: {current_lr:.6f}")

        # 更新实时图表
        if app and hasattr(app, 'training_active') and app.training_active:
            if hasattr(app, 'root') and hasattr(app, 'update_charts'):
                app.root.after(0, lambda: app.update_charts(epoch + 1, train_loss, val_loss, train_acc, val_acc))

        # 清理GPU内存
        if hasattr(trainer, 'gpu_manager'):
            trainer.gpu_manager.clear_cache()

    # 保存最终模型
    torch.save(trainer.model.state_dict(), final_model_path)
    print(f"💾 最终模型已保存: {os.path.basename(final_model_path)}")

    # 绘制训练历史
    plot_training_history(train_losses, val_losses, train_accs, val_accs, model_version, TRAINING_LOGS_DIR)
    return trainer.model

def plot_training_history(train_losses, val_losses, train_accs, val_accs, model_version, save_dir):
    """绘制训练历史 - 使用英文标题"""
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Training Loss', color='blue')
    if val_losses:
        plt.plot(val_losses, label='Validation Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Loss History')
    plt.grid(True, alpha=0.3)

    plt.subplot(1, 2, 2)
    plt.plot(train_accs, label='Training Accuracy', color='blue')
    if val_accs:
        plt.plot(val_accs, label='Validation Accuracy', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.title('Accuracy History')
    plt.grid(True, alpha=0.3)

    plt.suptitle(f"Training History: {model_version}", fontsize=14)
    plt.tight_layout(rect=[0, 0, 1, 0.96])

    plot_filename = f"training_history_{model_version}.png"
    plot_path = os.path.join(save_dir, plot_filename)
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"📈 训练历史图已保存: {plot_filename}")

def verify_image_optimized(model, image_path, transform, device='cuda', num_augmentations=10, threshold=0.5):
    """优化的图像验证函数"""
    model.to(device).double()  # 验证时使用双精度
    model.eval()
    print(f"🔍 验证图像: {os.path.basename(image_path)}")

    # 预处理基础图像
    pil_image_processed_base = preprocess_image_cpu(image_path)

    # 轻量级增强
    pil_augment_transform_verify = transforms.Compose([
        transforms.RandomAffine(degrees=(-15, 15), translate=(0.02, 0.02), scale=(0.98, 1.02), fill=0),
        transforms.ColorJitter(brightness=0.05, contrast=0.05, saturation=0.02, hue=0.02),
        transforms.RandomApply([transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 0.3))], p=0.1)
    ])

    match_degrees = []
    batch_size = 8 if device.type == 'cuda' else 4

    # 准备所有图像
    all_images = []

    # 原始图像（多次测试）
    for i in range(3):
        all_images.append(pil_image_processed_base)

    # 增强图像
    for i in range(max(0, num_augmentations - 3)):
        augmented_pil = pil_augment_transform_verify(pil_image_processed_base)
        all_images.append(augmented_pil)

        if i == 0:  # 保存一个增强样本
            aug_verify_path = os.path.join(SAMPLES_DIR, f"verify_augmented_{os.path.basename(image_path)}")
            try:
                augmented_pil.save(aug_verify_path)
            except Exception as e:
                print(f"❌ 保存增强验证样本失败: {e}")

    # 批量推理
    with torch.no_grad():
        for i in range(0, len(all_images), batch_size):
            batch_images = all_images[i:i+batch_size]
            batch_tensors = torch.stack([transform(img) for img in batch_images]).to(device).double()

            outputs = model(batch_tensors)
            batch_probs = torch.sigmoid(outputs).cpu().numpy().flatten()
            match_degrees.extend(batch_probs)

    avg_match_degree = np.mean(match_degrees)
    is_genuine = avg_match_degree >= threshold

    print(f"📊 平均匹配度: {avg_match_degree:.4f}")
    print(f"🎯 结果: {'真实' if is_genuine else '伪造'}")

    return {
        "image_path": image_path,
        "match_degrees": match_degrees,
        "match_degree": avg_match_degree,
        "is_genuine": is_genuine,
        "result": "真实" if is_genuine else "伪造",
        "threshold_used": threshold
    }
