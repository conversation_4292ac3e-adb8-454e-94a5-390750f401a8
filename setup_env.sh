#!/bin/bash
# PUF Authentication System - 虚拟环境设置脚本 (Linux/Mac)
# 此脚本将自动创建虚拟环境并安装所需依赖

echo "========================================"
echo "PUF Authentication System 环境设置"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.8或更高版本"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "检测到Python版本:"
$PYTHON_CMD --version
echo

# 检查pip是否可用
if ! $PYTHON_CMD -m pip --version &> /dev/null; then
    echo "错误: pip不可用，请检查Python安装"
    exit 1
fi

echo "检测到pip版本:"
$PYTHON_CMD -m pip --version
echo

# 创建虚拟环境
echo "正在创建虚拟环境..."
if [ -d "venv" ]; then
    echo "虚拟环境已存在，是否重新创建？ [y/N]"
    read -r choice
    if [[ $choice =~ ^[Yy]$ ]]; then
        echo "删除现有虚拟环境..."
        rm -rf venv
    else
        echo "使用现有虚拟环境..."
        source venv/bin/activate
        if [ $? -ne 0 ]; then
            echo "错误: 激活现有虚拟环境失败，将重新创建"
            rm -rf venv
        else
            echo "虚拟环境激活成功！"
            goto_install=true
        fi
    fi
fi

if [ "$goto_install" != true ]; then
    $PYTHON_CMD -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误: 创建虚拟环境失败"
        echo "请确保安装了python3-venv包"
        echo "Ubuntu/Debian: sudo apt install python3-venv"
        exit 1
    fi
    echo "虚拟环境创建成功！"
    echo

    # 激活虚拟环境
    echo "激活虚拟环境..."
    source venv/bin/activate
    if [ $? -ne 0 ]; then
        echo "错误: 激活虚拟环境失败"
        exit 1
    fi
fi

# 升级pip
echo "升级pip..."
python -m pip install --upgrade pip

# 安装依赖
echo
echo "正在安装依赖库..."
echo "这可能需要几分钟时间，请耐心等待..."
echo

# 尝试使用清华镜像源加速下载
echo "尝试使用清华镜像源..."
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
if [ $? -ne 0 ]; then
    echo "警告: 使用镜像源安装失败，尝试使用官方源..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 安装依赖失败"
        echo "请检查网络连接或手动安装依赖"
        exit 1
    fi
fi

echo
echo "========================================"
echo "环境设置完成！"
echo "========================================"
echo
echo "使用方法:"
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 运行程序: python main.py"
echo "3. 退出虚拟环境: deactivate"
echo

# 检查PyTorch是否正确安装
echo "检查PyTorch安装..."
python -c "import torch; print('PyTorch版本:', torch.__version__); print('CUDA可用:', torch.cuda.is_available())" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: PyTorch可能未正确安装"
else
    echo "PyTorch安装正常！"
fi

echo
echo "是否现在运行程序？ [y/N]"
read -r run_choice
if [[ $run_choice =~ ^[Yy]$ ]]; then
    echo "启动程序..."
    python main.py
fi

echo
echo "脚本执行完成！"
