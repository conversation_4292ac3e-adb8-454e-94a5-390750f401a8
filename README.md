# PUF Authentication System

基于深度学习的物理不可克隆函数（PUF）认证系统，使用PyTorch和AlexNet神经网络进行训练和验证。

## 🌟 项目特点

- **模块化设计** - 核心训练模块与GUI界面完全分离
- **深度学习** - 基于AlexNet的卷积神经网络
- **GPU加速** - 支持CUDA GPU训练，显著提升训练速度
- **实时监控** - 训练过程可视化，实时显示损失和准确率
- **图像处理** - 自动图像预处理和数据增强
- **用户友好** - 直观的图形用户界面

## 📁 项目结构

```
PUF_Authentication_System/
├── main.py              # 主程序入口
├── puf_core.py          # 神经网络训练核心模块
├── puf_gui.py           # GUI界面模块
├── requirements.txt     # 依赖库列表
├── PUF_Auth.spec       # PyInstaller打包配置
├── icon.ico            # 程序图标
├── README.md           # 项目说明文档
├── setup_env.bat       # 虚拟环境设置脚本(Windows)
├── setup_env.sh        # 虚拟环境设置脚本(Linux/Mac)
└── results/            # 结果目录
    ├── trained_models/     # 训练好的模型
    ├── sample_images/      # 样本图像
    ├── training_logs/      # 训练日志
    └── preview_samples/    # 预览样本
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8 或更高版本
- Windows 10/11, macOS, 或 Linux
- 推荐：NVIDIA GPU + CUDA（可选，用于加速训练）

### 2. 安装依赖

#### 方法一：使用虚拟环境（推荐）

**Windows:**
```bash
# 运行自动设置脚本
setup_env.bat

# 或手动设置
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

**Linux/Mac:**
```bash
# 运行自动设置脚本
chmod +x setup_env.sh
./setup_env.sh

# 或手动设置
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 方法二：直接安装
```bash
pip install -r requirements.txt
```

### 3. 运行程序

```bash
python main.py
```

## 📖 使用指南

### 训练模型

1. **准备数据**
   - 准备真实PUF图像（正样本）
   - 准备伪造PUF图像（负样本）
   - 支持格式：JPG, PNG, BMP

2. **设置参数**
   - 数据增强数量：建议500-1000
   - 批次大小：GPU推荐32，CPU推荐16
   - 训练轮数：建议30-100
   - 学习率：建议0.001

3. **开始训练**
   - 点击"开始训练"按钮
   - 实时查看训练进度和图表
   - 模型自动保存到results/trained_models/

### 验证图像

1. **选择模型**
   - 从训练好的模型列表中选择
   - 支持.pth格式的PyTorch模型

2. **选择图像**
   - 选择要验证的PUF图像
   - 系统会自动预处理

3. **查看结果**
   - 显示匹配度百分比
   - 给出真实/伪造判断

## 🔧 高级功能

### 模型配置

- **精度选择**: 支持半精度、单精度、双精度
- **设备选择**: 自动检测或手动选择CPU/GPU
- **学习率调度**: 支持plateau、step、cosine调度
- **早停机制**: 防止过拟合

### 数据处理

- **自动预处理**: 图像裁剪、缩放、圆形蒙版
- **数据增强**: 旋转、平移、缩放、颜色变换
- **并行处理**: 多进程图像预处理

### 训练监控

- **实时图表**: 损失和准确率曲线
- **日志记录**: 详细的训练日志
- **模型保存**: 自动保存最佳模型

## 📦 打包部署

### 创建可执行文件

```bash
# 安装PyInstaller
pip install pyinstaller

# 使用配置文件打包
pyinstaller PUF_Auth.spec

# 生成的文件位于 dist/PUFAuth/ 目录
```

### 分发说明

- 打包后的程序包含所有依赖
- 无需安装Python环境即可运行
- 文件较大（约1-2GB），因为包含PyTorch

## 🛠️ 开发指南

### 模块说明

#### puf_core.py - 核心模块
- `PUFDataset`: 数据集类
- `create_model()`: 模型创建
- `GPUTrainer/CPUTrainer`: 训练器
- `train_model_optimized()`: 训练函数
- `verify_image_optimized()`: 验证函数

#### puf_gui.py - 界面模块
- `PUFAuthenticationApp`: 主应用类
- `TextRedirector`: 日志重定向
- 界面布局和事件处理

### 扩展开发

1. **添加新模型**: 在puf_core.py中修改create_model()
2. **修改界面**: 在puf_gui.py中调整布局
3. **新增功能**: 保持模块间清晰分离

## 🔍 故障排除

### 常见问题

1. **CUDA错误**
   ```
   解决方案：检查CUDA版本兼容性，或切换到CPU模式
   ```

2. **内存不足**
   ```
   解决方案：减少批次大小或数据增强数量
   ```

3. **依赖库错误**
   ```
   解决方案：使用虚拟环境，按requirements.txt安装
   ```

### 性能优化

- **GPU训练**: 确保安装CUDA版本的PyTorch
- **内存管理**: 适当设置批次大小和工作线程数
- **数据预处理**: 使用多进程加速图像处理

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题，请查看故障排除部分或提交Issue。
