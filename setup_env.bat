@echo off
REM PUF Authentication System - 虚拟环境设置脚本 (Windows)
REM 此脚本将自动创建虚拟环境并安装所需依赖

echo ========================================
echo PUF Authentication System 环境设置
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version
echo.

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: pip不可用，请检查Python安装
    pause
    exit /b 1
)

echo 检测到pip版本:
pip --version
echo.

REM 创建虚拟环境
echo 正在创建虚拟环境...
if exist venv (
    echo 虚拟环境已存在，是否重新创建？ [Y/N]
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 删除现有虚拟环境...
        rmdir /s /q venv
    ) else (
        echo 使用现有虚拟环境...
        goto activate_env
    )
)

python -m venv venv
if errorlevel 1 (
    echo 错误: 创建虚拟环境失败
    pause
    exit /b 1
)
echo 虚拟环境创建成功！
echo.

:activate_env
REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo 错误: 激活虚拟环境失败
    pause
    exit /b 1
)

REM 升级pip
echo 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo.
echo 正在安装依赖库...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 使用清华镜像源加速下载
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
if errorlevel 1 (
    echo 警告: 使用镜像源安装失败，尝试使用官方源...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 安装依赖失败
        echo 请检查网络连接或手动安装依赖
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 环境设置完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 激活虚拟环境: venv\Scripts\activate
echo 2. 运行程序: python main.py
echo 3. 退出虚拟环境: deactivate
echo.

REM 检查PyTorch是否正确安装
echo 检查PyTorch安装...
python -c "import torch; print('PyTorch版本:', torch.__version__); print('CUDA可用:', torch.cuda.is_available())" 2>nul
if errorlevel 1 (
    echo 警告: PyTorch可能未正确安装
) else (
    echo PyTorch安装正常！
)

echo.
echo 是否现在运行程序？ [Y/N]
set /p run_choice=
if /i "%run_choice%"=="Y" (
    echo 启动程序...
    python main.py
)

echo.
echo 按任意键退出...
pause >nul
