"""
PUF Authentication System - GUI界面模块
包含用户界面相关的代码，调用核心训练模块进行实际的训练和验证
"""

import os
import sys
import time
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from tkinter.scrolledtext import ScrolledText
import threading
import multiprocessing
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import torchvision.transforms as transforms
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure

# 导入核心模块
from puf_core import (
    check_gpu_status, set_seed, get_optimal_workers, 
    PUFDataset, create_model, GPUTrainer, CPUTrainer,
    train_model_optimized, verify_image_optimized, preprocess_image,
    MODELS_DIR, SAMPLES_DIR, PREVIEW_SAMPLES_DIR
)

class TextRedirector:
    """简化的文本重定向类"""
    def __init__(self, text_widget):
        self.text_widget = text_widget
        self.buffer = ""

    def write(self, string):
        self.buffer += string
        if '\n' in self.buffer or len(self.buffer) > 512:
            if self.text_widget.winfo_exists():
                self.text_widget.insert(tk.END, self.buffer)
                self.text_widget.see(tk.END)
                self.text_widget.update_idletasks()
            self.buffer = ""

    def flush(self):
        if self.buffer and self.text_widget.winfo_exists():
            self.text_widget.insert(tk.END, self.buffer)
            self.text_widget.see(tk.END)
            self.text_widget.update_idletasks()
            self.buffer = ""

class PUFAuthenticationApp:
    """主应用程序"""
    PREVIEW_WIDTH_PX = 200
    PREVIEW_HEIGHT_PX = 200

    def __init__(self, root):
        self.root = root
        self.root.title("PUF Authentication System - Enhanced")
        self.root.geometry("1600x1000")
        self.root.resizable(True, True)

        # 训练状态控制
        self.training_active = False
        self.force_stop_training = False

        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧：控制面板 (30%)
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        left_frame.config(width=480)  # 固定宽度

        # 右侧：图表和日志 (70%)
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # ImageTk引用
        self.positive_original_img_tk = None
        self.positive_processed_img_tk = None
        self.negative_original_img_tk = None
        self.negative_processed_img_tk = None
        self.verify_original_img_tk = None
        self.verify_processed_img_tk = None

        self.model_files = []
        self.start_model_files = []

        # 训练数据
        self.training_data = {
            'epochs': [],
            'train_losses': [],
            'val_losses': [],
            'train_accs': [],
            'val_accs': []
        }

        # 设置界面
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        self.refresh_all_model_lists()

    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        # 创建notebook
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        self.train_frame = ttk.Frame(self.notebook)
        self.verify_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.train_frame, text="训练")
        self.notebook.add(self.verify_frame, text="验证")

        self.setup_train_tab()
        self.setup_verify_tab()

    def setup_right_panel(self, parent):
        """设置右侧图表和日志面板"""
        # 上半部分：训练图表 (60%)
        chart_frame = ttk.LabelFrame(parent, text="训练进度")
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # 创建matplotlib图表
        self.fig = Figure(figsize=(10, 6), dpi=100)
        self.ax1 = self.fig.add_subplot(211)
        self.ax2 = self.fig.add_subplot(212)

        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 初始化图表
        self.init_charts()

        # 下半部分：日志 (40%)
        log_frame = ttk.LabelFrame(parent, text="训练日志")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 创建日志文本框
        self.log_text = ScrolledText(log_frame, height=12, wrap=tk.WORD, relief=tk.SUNKEN, borderwidth=1)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 重定向输出到日志
        sys.stdout = TextRedirector(self.log_text)
        sys.stderr = TextRedirector(self.log_text)
        print("📝 日志系统初始化完成")

    def init_charts(self):
        """初始化图表 - 使用英文标题"""
        self.ax1.set_title('Training Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True, alpha=0.3)

        self.ax2.set_title('Training Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy')
        self.ax2.grid(True, alpha=0.3)

        self.fig.tight_layout()
        self.canvas.draw()

    def update_charts(self, epoch, train_loss, val_loss, train_acc, val_acc):
        """更新训练图表"""
        self.training_data['epochs'].append(epoch)
        self.training_data['train_losses'].append(train_loss)
        self.training_data['train_accs'].append(train_acc)

        if val_loss is not None:
            self.training_data['val_losses'].append(val_loss)
            self.training_data['val_accs'].append(val_acc)

        # 清除并重绘
        self.ax1.clear()
        self.ax2.clear()

        # 损失图
        self.ax1.plot(self.training_data['epochs'], self.training_data['train_losses'],
                     label='Train Loss', color='blue', linewidth=2)
        if self.training_data['val_losses']:
            self.ax1.plot(self.training_data['epochs'], self.training_data['val_losses'],
                         label='Val Loss', color='red', linewidth=2)
        self.ax1.set_title('Training Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)

        # 准确率图
        self.ax2.plot(self.training_data['epochs'], self.training_data['train_accs'],
                     label='Train Acc', color='blue', linewidth=2)
        if self.training_data['val_accs']:
            self.ax2.plot(self.training_data['epochs'], self.training_data['val_accs'],
                         label='Val Acc', color='red', linewidth=2)
        self.ax2.set_title('Training Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy')
        self.ax2.legend()
        self.ax2.grid(True, alpha=0.3)

        self.fig.tight_layout()
        self.canvas.draw()

    def clear_charts(self):
        """清除图表数据"""
        self.training_data = {
            'epochs': [],
            'train_losses': [],
            'val_losses': [],
            'train_accs': [],
            'val_accs': []
        }
        self.init_charts()

    def setup_train_tab(self):
        main_train_frame = ttk.Frame(self.train_frame)
        main_train_frame.pack(fill=tk.BOTH, expand=True)

        main_train_frame.columnconfigure(0, weight=1)
        main_train_frame.columnconfigure(1, weight=1)

        # 左侧控制区
        left_control_frame = ttk.Frame(main_train_frame)
        left_control_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # 右侧预览区
        preview_frame = ttk.LabelFrame(main_train_frame, text="图像预览")
        preview_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # 输入图像区域
        inputs_frame = ttk.LabelFrame(left_control_frame, text="输入图像")
        inputs_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 训练参数区域
        params_frame = ttk.LabelFrame(left_control_frame, text="训练参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 训练控制区域
        control_frame = ttk.LabelFrame(left_control_frame, text="训练控制")
        control_frame.pack(fill=tk.X, pady=5)

        # === 输入图像区域 ===
        ttk.Label(inputs_frame, text="真实图像:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.positive_listbox = tk.Listbox(inputs_frame, selectmode=tk.EXTENDED, height=6)
        self.positive_listbox.grid(row=1, column=0, padx=5, pady=2, sticky="ew")
        pos_scrollbar = ttk.Scrollbar(inputs_frame, orient="vertical", command=self.positive_listbox.yview)
        pos_scrollbar.grid(row=1, column=1, sticky="ns")
        self.positive_listbox.config(yscrollcommand=pos_scrollbar.set)
        self.positive_listbox.bind('<<ListboxSelect>>', lambda event: self.on_listbox_select(event, "positive"))

        pos_btn_frame = ttk.Frame(inputs_frame)
        pos_btn_frame.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        ttk.Button(pos_btn_frame, text="添加", command=lambda: self.browse_images(self.positive_listbox, "positive")).pack(side=tk.LEFT, padx=2)
        ttk.Button(pos_btn_frame, text="删除", command=lambda: self.remove_selected(self.positive_listbox, "positive")).pack(side=tk.LEFT, padx=2)
        ttk.Button(pos_btn_frame, text="清空", command=lambda: self.clear_list(self.positive_listbox, "positive")).pack(side=tk.LEFT, padx=2)

        ttk.Label(inputs_frame, text="伪造图像:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=(10, 2))
        self.negative_listbox = tk.Listbox(inputs_frame, selectmode=tk.EXTENDED, height=6)
        self.negative_listbox.grid(row=4, column=0, padx=5, pady=2, sticky="ew")
        neg_scrollbar = ttk.Scrollbar(inputs_frame, orient="vertical", command=self.negative_listbox.yview)
        neg_scrollbar.grid(row=4, column=1, sticky="ns")
        self.negative_listbox.config(yscrollcommand=neg_scrollbar.set)
        self.negative_listbox.bind('<<ListboxSelect>>', lambda event: self.on_listbox_select(event, "negative"))

        neg_btn_frame = ttk.Frame(inputs_frame)
        neg_btn_frame.grid(row=5, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        ttk.Button(neg_btn_frame, text="添加", command=lambda: self.browse_images(self.negative_listbox, "negative")).pack(side=tk.LEFT, padx=2)
        ttk.Button(neg_btn_frame, text="删除", command=lambda: self.remove_selected(self.negative_listbox, "negative")).pack(side=tk.LEFT, padx=2)
        ttk.Button(neg_btn_frame, text="清空", command=lambda: self.clear_list(self.negative_listbox, "negative")).pack(side=tk.LEFT, padx=2)

        inputs_frame.columnconfigure(0, weight=1)
        inputs_frame.rowconfigure(1, weight=1)
        inputs_frame.rowconfigure(4, weight=1)

        # === 图像预览区域 ===
        ph_bg = "gray80"

        ttk.Label(preview_frame, text="真实原图:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.positive_original_label = tk.Label(preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.positive_original_label.grid(row=1, column=0, padx=5, pady=2)

        ttk.Label(preview_frame, text="真实处理:").grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        self.positive_processed_label = tk.Label(preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.positive_processed_label.grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(preview_frame, text="伪造原图:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=(10, 2))
        self.negative_original_label = tk.Label(preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.negative_original_label.grid(row=3, column=0, padx=5, pady=2)

        ttk.Label(preview_frame, text="伪造处理:").grid(row=2, column=1, sticky=tk.W, padx=5, pady=(10, 2))
        self.negative_processed_label = tk.Label(preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.negative_processed_label.grid(row=3, column=1, padx=5, pady=2)

        preview_frame.columnconfigure(0, weight=1)
        preview_frame.columnconfigure(1, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        preview_frame.rowconfigure(3, weight=1)

        # === 训练参数区域 ===
        r = 0
        ttk.Label(params_frame, text="继续模型:").grid(row=r, column=0, sticky=tk.W, padx=5, pady=2)
        self.start_model_var = tk.StringVar()
        self.start_model_combo = ttk.Combobox(params_frame, textvariable=self.start_model_var, width=25, state="readonly")
        self.start_model_combo.grid(row=r, column=1, sticky="ew", padx=5, pady=2)
        ttk.Button(params_frame, text="刷新", width=8, command=self.refresh_start_model_list).grid(row=r, column=2, padx=5, pady=2, sticky=tk.E)

        r += 1
        ttk.Label(params_frame, text="数据增强:").grid(row=r, column=0, sticky=tk.W, padx=5, pady=2)
        self.augmentations_var = tk.IntVar(value=500)
        ttk.Entry(params_frame, textvariable=self.augmentations_var, width=10).grid(row=r, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(params_frame, text="批次大小:").grid(row=r, column=2, sticky=tk.W, padx=5, pady=2)
        default_batch_size = 32 if torch.cuda.is_available() else 16
        self.batch_size_var = tk.IntVar(value=default_batch_size)
        ttk.Entry(params_frame, textvariable=self.batch_size_var, width=8).grid(row=r, column=3, sticky=tk.W, padx=5, pady=2)

        r += 1
        ttk.Label(params_frame, text="训练轮数:").grid(row=r, column=0, sticky=tk.W, padx=5, pady=2)
        self.epochs_var = tk.IntVar(value=30)
        ttk.Entry(params_frame, textvariable=self.epochs_var, width=10).grid(row=r, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(params_frame, text="学习率:").grid(row=r, column=2, sticky=tk.W, padx=5, pady=2)
        self.learning_rate_var = tk.DoubleVar(value=0.001)
        ttk.Entry(params_frame, textvariable=self.learning_rate_var, width=8).grid(row=r, column=3, sticky=tk.W, padx=5, pady=2)

        r += 1
        ttk.Label(params_frame, text="工作线程:").grid(row=r, column=0, sticky=tk.W, padx=5, pady=2)
        self.num_workers_var = tk.IntVar(value=get_optimal_workers())
        ttk.Entry(params_frame, textvariable=self.num_workers_var, width=10).grid(row=r, column=1, sticky=tk.W, padx=5, pady=2)

        # 添加早停机制控制
        ttk.Label(params_frame, text="早停步数:").grid(row=r, column=2, sticky=tk.W, padx=5, pady=2)
        self.early_stop_patience_var = tk.IntVar(value=12)  # 默认12步
        ttk.Entry(params_frame, textvariable=self.early_stop_patience_var, width=8).grid(row=r, column=3, sticky=tk.W, padx=5, pady=2)

        r += 1
        ttk.Label(params_frame, text="模型版本:").grid(row=r, column=0, sticky=tk.W, padx=5, pady=2)
        self.model_version_var = tk.StringVar(value=time.strftime("%Y%m%d_%H%M%S"))
        ttk.Entry(params_frame, textvariable=self.model_version_var, width=15).grid(row=r, column=1, columnspan=2, sticky="ew", padx=5, pady=2)

        r += 1
        # 计算设备和精度选择
        device_frame = ttk.Frame(params_frame)
        device_frame.grid(row=r, column=0, columnspan=4, sticky=tk.W, padx=5, pady=2)

        self.compute_device_var = tk.StringVar(value="auto")
        ttk.Label(device_frame, text="设备:").pack(side=tk.LEFT)
        device_options = ["auto", "cpu", "gpu"] if torch.cuda.is_available() else ["auto", "cpu"]
        device_combo = ttk.Combobox(device_frame, textvariable=self.compute_device_var, values=device_options, state="readonly", width=8)
        device_combo.pack(side=tk.LEFT, padx=5)

        # GPU精度选择
        ttk.Label(device_frame, text="GPU精度:").pack(side=tk.LEFT, padx=(10, 5))
        self.gpu_precision_var = tk.StringVar(value="double")
        gpu_precision_combo = ttk.Combobox(device_frame, textvariable=self.gpu_precision_var,
                                         values=["half", "float", "double"], state="readonly", width=8)
        gpu_precision_combo.pack(side=tk.LEFT, padx=5)

        # CPU精度选择
        ttk.Label(device_frame, text="CPU精度:").pack(side=tk.LEFT, padx=(10, 5))
        self.cpu_precision_var = tk.StringVar(value="double")
        cpu_precision_combo = ttk.Combobox(device_frame, textvariable=self.cpu_precision_var,
                                         values=["float", "double"], state="readonly", width=8)
        cpu_precision_combo.pack(side=tk.LEFT, padx=5)

        r += 1
        options_frame = ttk.Frame(params_frame)
        options_frame.grid(row=r, column=0, columnspan=4, sticky=tk.W, padx=5, pady=2)

        self.use_cpu_preprocessing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="CPU并行预处理", variable=self.use_cpu_preprocessing_var).pack(side=tk.LEFT)

        # 学习率调度选项
        self.lr_scheduler_var = tk.StringVar(value="plateau")
        ttk.Label(options_frame, text="学习率调度:").pack(side=tk.LEFT, padx=(20, 5))
        lr_scheduler_combo = ttk.Combobox(options_frame, textvariable=self.lr_scheduler_var,
                                        values=["plateau", "step", "cosine", "none"], state="readonly", width=10)
        lr_scheduler_combo.pack(side=tk.LEFT, padx=5)

        params_frame.columnconfigure(1, weight=1)

        # === 训练控制区域 ===
        control_btn_frame = ttk.Frame(control_frame)
        control_btn_frame.pack(fill=tk.X, pady=5)

        # 开始训练按钮
        self.start_train_btn = ttk.Button(control_btn_frame, text="🚀 开始训练", command=self.start_training_thread)
        self.start_train_btn.pack(side=tk.LEFT, padx=5)

        # 强制停止按钮
        self.stop_train_btn = ttk.Button(control_btn_frame, text="⏹️ 强制停止", command=self.force_stop_training_action, state="disabled")
        self.stop_train_btn.pack(side=tk.LEFT, padx=5)

        # 清空日志按钮
        ttk.Button(control_btn_frame, text="🗑️ 清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)

    def force_stop_training_action(self):
        """强制停止训练"""
        self.force_stop_training = True
        self.training_active = False
        self.start_train_btn.config(state="normal")
        self.stop_train_btn.config(state="disabled")
        print("⏹️ 用户请求强制停止训练...")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        print("🗑️ 日志已清空")

    def _resize_pil_for_preview(self, pil_image, target_width, target_height, maintain_aspect=True, bg_color=(220, 220, 220)):
        """调整预览图像大小"""
        if pil_image is None:
            return None

        try:
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            if maintain_aspect:
                original_width, original_height = pil_image.size
                ratio = min(target_width / original_width, target_height / original_height)
                new_width = int(original_width * ratio)
                new_height = int(original_height * ratio)

                resized_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                final_image = Image.new("RGB", (target_width, target_height), bg_color)
                paste_x = (target_width - new_width) // 2
                paste_y = (target_height - new_height) // 2
                final_image.paste(resized_image, (paste_x, paste_y))
                return final_image
            else:
                return pil_image.resize((target_width, target_height), Image.Resampling.LANCZOS)
        except Exception as e:
            print(f"❌ 调整图像大小失败: {e}")
            error_img = Image.new('RGB', (target_width, target_height), (240, 128, 128))
            return error_img

    def update_preview_image(self, image_path, image_type, label_orig, label_proc, img_tk_orig_attr, img_tk_proc_attr):
        """更新预览图像"""
        ph_text = "N/A"
        ph_bg = "gray80"
        try:
            if image_path and os.path.exists(image_path):
                # 原始预览
                img_pil_orig = Image.open(image_path)
                if img_pil_orig.mode == 'RGBA':
                    background = Image.new("RGB", img_pil_orig.size, (255, 255, 255))
                    background.paste(img_pil_orig, mask=img_pil_orig.split()[3])
                    img_pil_orig = background
                elif img_pil_orig.mode != 'RGB':
                    img_pil_orig = img_pil_orig.convert('RGB')

                img_pil_orig_preview = self._resize_pil_for_preview(img_pil_orig, self.PREVIEW_WIDTH_PX, self.PREVIEW_HEIGHT_PX, maintain_aspect=True)
                img_tk_orig = ImageTk.PhotoImage(img_pil_orig_preview)
                setattr(self, img_tk_orig_attr, img_tk_orig)
                label_orig.config(image=img_tk_orig, text="", width=0, height=0)

                # 处理预览
                try:
                    img_pil_proc_full = preprocess_image(image_path)
                    img_pil_proc_preview = self._resize_pil_for_preview(img_pil_proc_full, self.PREVIEW_WIDTH_PX, self.PREVIEW_HEIGHT_PX, maintain_aspect=False)
                    img_tk_proc = ImageTk.PhotoImage(img_pil_proc_preview)
                    setattr(self, img_tk_proc_attr, img_tk_proc)
                    label_proc.config(image=img_tk_proc, text="", width=0, height=0)

                    # 保存预览样本
                    base, _ = os.path.splitext(os.path.basename(image_path))
                    orig_preview_save_path = os.path.join(PREVIEW_SAMPLES_DIR, f"gui_preview_{image_type}_{base}_original.png")
                    proc_preview_save_path = os.path.join(PREVIEW_SAMPLES_DIR, f"gui_preview_{image_type}_{base}_processed_512.png")
                    img_pil_orig_preview.save(orig_preview_save_path)
                    img_pil_proc_full.save(proc_preview_save_path)
                except Exception as e:
                    print(f"❌ 预处理预览失败: {e}")
                    label_proc.config(image="", text="处理错误", bg="#F08080", width=10, height=5)
            else:
                label_orig.config(image="", text=ph_text, bg=ph_bg, width=10, height=5)
                label_proc.config(image="", text=ph_text, bg=ph_bg, width=10, height=5)
                setattr(self, img_tk_orig_attr, None)
                setattr(self, img_tk_proc_attr, None)

        except Exception as e:
            print(f"❌ 更新{image_type}预览失败: {e}")
            label_orig.config(image="", text="加载错误", bg="#F08080", width=10, height=5)
            label_proc.config(image="", text="加载错误", bg="#F08080", width=10, height=5)
            setattr(self, img_tk_orig_attr, None)
            setattr(self, img_tk_proc_attr, None)

    def on_listbox_select(self, event, image_type):
        listbox = event.widget
        selected_indices = listbox.curselection()
        if selected_indices:
            selected_path = listbox.get(selected_indices[0])
            if image_type == "positive":
                self.update_preview_image(selected_path, "positive", self.positive_original_label, self.positive_processed_label, 'positive_original_img_tk', 'positive_processed_img_tk')
            elif image_type == "negative":
                self.update_preview_image(selected_path, "negative", self.negative_original_label, self.negative_processed_label, 'negative_original_img_tk', 'negative_processed_img_tk')

    def browse_images(self, listbox, image_type):
        filenames = filedialog.askopenfilenames(
            title=f"选择{image_type.capitalize()}图像",
            filetypes=[("图像文件", "*.bmp *.jpg *.jpeg *.png"), ("所有文件", "*.*")]
        )
        if filenames:
            current_items = set(listbox.get(0, tk.END))
            new_items_added = False
            for fn in filenames:
                if fn not in current_items:
                    listbox.insert(tk.END, fn)
                    new_items_added = True

            if new_items_added and listbox.size() > 0:
                listbox.selection_clear(0, tk.END)
                listbox.selection_set(0)
                listbox.event_generate("<<ListboxSelect>>")

    def remove_selected(self, listbox, image_type):
        selected_indices = listbox.curselection()
        for i in reversed(selected_indices):
            listbox.delete(i)

        if listbox.size() > 0:
            listbox.selection_set(0)
        listbox.event_generate("<<ListboxSelect>>")

    def clear_list(self, listbox, image_type):
        listbox.delete(0, tk.END)
        listbox.event_generate("<<ListboxSelect>>")

    def setup_verify_tab(self):
        verify_content_frame = ttk.Frame(self.verify_frame)
        verify_content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 上部分：图像选择和模型选择
        top_frame = ttk.Frame(verify_content_frame)
        top_frame.pack(fill=tk.X, pady=5)

        img_select_frame = ttk.LabelFrame(top_frame, text="验证图像")
        img_select_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.verify_img_var = tk.StringVar()
        ttk.Entry(img_select_frame, textvariable=self.verify_img_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        ttk.Button(img_select_frame, text="浏览...", command=self.browse_verify_img).pack(side=tk.LEFT, padx=5, pady=5)

        model_select_frame = ttk.LabelFrame(top_frame, text="验证模型")
        model_select_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        self.verify_model_listbox = tk.Listbox(model_select_frame, height=4, exportselection=False)
        self.verify_model_listbox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        verify_model_scroll = ttk.Scrollbar(model_select_frame, orient="vertical", command=self.verify_model_listbox.yview)
        verify_model_scroll.pack(side=tk.LEFT, fill=tk.Y, pady=5)
        self.verify_model_listbox.config(yscrollcommand=verify_model_scroll.set)
        ttk.Button(model_select_frame, text="刷新", command=self.refresh_verify_model_list).pack(side=tk.LEFT, padx=5, pady=5, anchor='n')

        # 中部分：验证图像预览
        verify_preview_frame = ttk.LabelFrame(verify_content_frame, text="验证图像预览")
        verify_preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ph_bg = "gray80"
        ttk.Label(verify_preview_frame, text="原图:").grid(row=0, column=0, padx=5, pady=2, sticky='sw')
        self.verify_original_label = tk.Label(verify_preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.verify_original_label.grid(row=1, column=0, padx=10, pady=5)

        ttk.Label(verify_preview_frame, text="处理后:").grid(row=0, column=1, padx=5, pady=2, sticky='sw')
        self.verify_processed_label = tk.Label(verify_preview_frame, text="N/A", relief="solid", borderwidth=1, width=10, height=5, bg=ph_bg)
        self.verify_processed_label.grid(row=1, column=1, padx=10, pady=5)

        verify_preview_frame.columnconfigure(0, weight=1)
        verify_preview_frame.columnconfigure(1, weight=1)
        verify_preview_frame.rowconfigure(1, weight=1)

        # 底部分：验证选项和按钮
        bottom_frame = ttk.Frame(verify_content_frame)
        bottom_frame.pack(fill=tk.X, pady=5)

        self.verify_device_var = tk.StringVar(value="auto")
        ttk.Label(bottom_frame, text="设备:").pack(side=tk.LEFT, padx=5)
        verify_device_options = ["auto", "cpu", "gpu"] if torch.cuda.is_available() else ["auto", "cpu"]
        verify_device_combo = ttk.Combobox(bottom_frame, textvariable=self.verify_device_var, values=verify_device_options, state="readonly", width=10)
        verify_device_combo.pack(side=tk.LEFT, padx=5)

        ttk.Label(bottom_frame, text="阈值:").pack(side=tk.LEFT, padx=5)
        self.verify_threshold_var = tk.DoubleVar(value=0.5)
        ttk.Entry(bottom_frame, textvariable=self.verify_threshold_var, width=8).pack(side=tk.LEFT, padx=5)

        ttk.Button(bottom_frame, text="🔍 验证图像", command=self.run_verification_thread).pack(side=tk.RIGHT, padx=5)

    def browse_verify_img(self):
        filename = filedialog.askopenfilename(
            title="选择验证图像",
            filetypes=[("图像文件", "*.bmp *.jpg *.jpeg *.png")]
        )
        if filename:
            self.verify_img_var.set(filename)
            self.update_preview_image(filename, "verify", self.verify_original_label, self.verify_processed_label, 'verify_original_img_tk', 'verify_processed_img_tk')

    def _get_model_files_from_dirs(self):
        """获取模型文件"""
        found_files = []
        if os.path.exists(MODELS_DIR):
            for file in os.listdir(MODELS_DIR):
                if file.endswith('.pth'):
                    full_path = os.path.join(MODELS_DIR, file)
                    found_files.append(full_path)
        found_files.sort(key=lambda f: os.path.getmtime(f), reverse=True)
        return found_files

    def refresh_start_model_list(self):
        self.start_model_files = self._get_model_files_from_dirs()
        model_names = ["<无>"] + [os.path.basename(f) for f in self.start_model_files]
        self.start_model_combo['values'] = model_names
        current_selection = self.start_model_var.get()
        if current_selection in model_names:
            self.start_model_combo.set(current_selection)
        elif model_names:
            self.start_model_combo.set("<无>")

    def refresh_verify_model_list(self):
        self.verify_model_listbox.delete(0, tk.END)
        self.model_files = self._get_model_files_from_dirs()
        for file_path in self.model_files:
            self.verify_model_listbox.insert(tk.END, os.path.basename(file_path))
        if self.model_files:
            self.verify_model_listbox.selection_set(0)

    def refresh_all_model_lists(self):
        self.refresh_start_model_list()
        self.refresh_verify_model_list()

    def start_training_thread(self):
        positive_imgs = list(self.positive_listbox.get(0, tk.END))
        negative_imgs = list(self.negative_listbox.get(0, tk.END))

        if not positive_imgs:
            messagebox.showerror("错误", "至少需要一张真实图像")
            return
        if not negative_imgs:
            messagebox.showerror("错误", "至少需要一张伪造图像")
            return

        for img_path in positive_imgs + negative_imgs:
            if not os.path.exists(img_path):
                messagebox.showerror("错误", f"图像文件不存在: {img_path}")
                return

        selected_start_model_name = self.start_model_var.get()
        start_model_path = None

        model_version = self.model_version_var.get()
        if not model_version:
            model_version = time.strftime("%Y%m%d_%H%M%S")
            self.model_version_var.set(model_version)

        if selected_start_model_name and selected_start_model_name != "<无>":
            for f_path in self.start_model_files:
                if os.path.basename(f_path) == selected_start_model_name:
                    start_model_path = f_path
                    model_version = f"{os.path.splitext(selected_start_model_name)[0]}_cont_{time.strftime('%H%M%S')}"
                    self.model_version_var.set(model_version)
                    break
            if not start_model_path:
                messagebox.showerror("错误", f"起始模型未找到: {selected_start_model_name}")
                return

        # 确定计算设备
        device_choice = self.compute_device_var.get()
        if device_choice == "auto":
            use_gpu = torch.cuda.is_available()
        elif device_choice == "gpu":
            use_gpu = torch.cuda.is_available()
            if not use_gpu:
                messagebox.showwarning("警告", "GPU不可用，使用CPU")
        else:
            use_gpu = False

        # 确定精度
        if use_gpu:
            precision = self.gpu_precision_var.get()
        else:
            precision = self.cpu_precision_var.get()

        params = {
            'positive_imgs': positive_imgs, 'negative_imgs': negative_imgs,
            'augmentations': self.augmentations_var.get(), 'batch_size': self.batch_size_var.get(),
            'epochs': self.epochs_var.get(), 'learning_rate': self.learning_rate_var.get(),
            'use_gpu': use_gpu, 'precision': precision, 'num_workers': self.num_workers_var.get(),
            'model_version': model_version, 'start_model_checkpoint_path': start_model_path,
            'use_cpu_preprocessing': self.use_cpu_preprocessing_var.get(),
            'lr_scheduler': self.lr_scheduler_var.get(),
            'early_stop_patience': self.early_stop_patience_var.get()  # 添加早停参数
        }

        if params['augmentations'] < 1:
            messagebox.showerror("错误", "数据增强数量必须 >= 1")
            return

        if params['early_stop_patience'] < 1:
            messagebox.showerror("错误", "早停步数必须 >= 1")
            return

        total_augmented = (len(positive_imgs) + len(negative_imgs)) * params['augmentations']
        if total_augmented < params['batch_size'] * 2 and total_augmented > 0:
            messagebox.showwarning("警告", f"总增强图像数({total_augmented})较少，考虑增加数据增强或减少批次大小")

        # 清除之前的训练数据和图表
        self.clear_charts()
        self.log_text.delete(1.0, tk.END)

        print("🚀 开始训练...")
        print(f"📊 模型版本: {params['model_version']}")
        print(f"💻 设备: {'GPU' if params['use_gpu'] else 'CPU'} ({params['precision']} precision)")
        print(f"🔧 工作线程: {params['num_workers']}")
        print(f"📈 学习率调度: {params['lr_scheduler']}")
        print(f"⏰ 早停步数: {params['early_stop_patience']}")
        if params['start_model_checkpoint_path']:
            print(f"🔄 继续训练: {os.path.basename(params['start_model_checkpoint_path'])}")

        # 重置强制停止标志
        self.force_stop_training = False
        self.training_active = True

        # 更新按钮状态
        self.start_train_btn.config(state="disabled")
        self.stop_train_btn.config(state="normal")

        thread = threading.Thread(target=self.execute_training_logic, args=(params,))
        thread.daemon = True
        thread.start()

    def execute_training_logic(self, params):
        """执行训练逻辑"""
        try:
            device = torch.device("cuda" if torch.cuda.is_available() and params['use_gpu'] else "cpu")
            print(f"🎯 使用设备: {device}")

            # 数据变换
            base_data_transform = transforms.Compose([
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

            print("📸 创建数据集...")
            print(f"  ✅ 正样本: {len(params['positive_imgs'])}")
            print(f"  ❌ 负样本: {len(params['negative_imgs'])}")

            # 创建数据集
            positive_dataset = PUFDataset(
                params['positive_imgs'], True, base_data_transform,
                params['augmentations'], use_cpu_preprocessing=params['use_cpu_preprocessing']
            )
            negative_dataset = PUFDataset(
                params['negative_imgs'], False, base_data_transform,
                params['augmentations'], use_cpu_preprocessing=params['use_cpu_preprocessing']
            )
            full_dataset = torch.utils.data.ConcatDataset([positive_dataset, negative_dataset])

            # 数据集分割
            total_samples = len(full_dataset)
            if total_samples == 0:
                self.root.after(0, lambda: messagebox.showerror("错误", "数据集为空"))
                return

            train_size = int(0.8 * total_samples)
            val_size = total_samples - train_size

            if val_size > 0 and val_size < params['batch_size']:
                val_size = 0
                train_size = total_samples
                print("⚠️  验证集太小，使用所有数据进行训练")
            elif train_size < params['batch_size'] and total_samples > 0:
                self.root.after(0, lambda: messagebox.showerror("错误", f"训练集({train_size})小于批次大小({params['batch_size']})"))
                return

            # 创建数据加载器
            if val_size > 0:
                train_dataset, val_dataset = random_split(full_dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42))
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=params['batch_size'],
                    shuffle=False,
                    num_workers=params['num_workers'],
                    pin_memory=params['use_gpu'],
                    persistent_workers=params['num_workers'] > 0
                )
            else:
                train_dataset = full_dataset
                val_loader = None

            train_loader = DataLoader(
                train_dataset,
                batch_size=params['batch_size'],
                shuffle=True,
                num_workers=params['num_workers'],
                generator=torch.Generator().manual_seed(42),
                pin_memory=params['use_gpu'],
                persistent_workers=params['num_workers'] > 0
            )

            print(f"📊 训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset) if val_loader else 0}")
            print(f"🔄 训练批次: {len(train_loader)}, 验证批次: {len(val_loader) if val_loader else 0}")

            # 创建模型
            model = create_model(precision=params['precision'])

            if params['start_model_checkpoint_path']:
                try:
                    checkpoint = torch.load(params['start_model_checkpoint_path'], map_location=device)
                    model.load_state_dict(checkpoint)
                    print(f"✅ 成功加载检查点: {os.path.basename(params['start_model_checkpoint_path'])}")
                except Exception as e:
                    print(f"❌ 加载检查点失败: {e}")
                    self.root.after(0, lambda: messagebox.showerror("检查点加载错误", str(e)))
                    return

            # 选择训练器
            if params['use_gpu'] and torch.cuda.is_available():
                trainer = GPUTrainer(model, device, params['precision'])
            else:
                trainer = CPUTrainer(model, device, params['precision'])

            # 损失函数和优化器
            criterion = torch.nn.BCEWithLogitsLoss()
            optimizer = optim.AdamW(model.parameters(), lr=params['learning_rate'], weight_decay=1e-4)

            # 学习率调度器
            scheduler = None
            if params['lr_scheduler'] == 'plateau':
                scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=3)
            elif params['lr_scheduler'] == 'step':
                scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.5)
            elif params['lr_scheduler'] == 'cosine':
                scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=params['epochs'])

            start_time = time.time()

            train_model_optimized(
                trainer, train_loader, val_loader, criterion, optimizer, scheduler,
                params['epochs'], device, params['model_version'], MODELS_DIR, self,
                early_stop_patience=params['early_stop_patience']  # 传递早停参数
            )

            training_time = time.time() - start_time
            print(f"🎉 训练完成! 时间: {training_time / 60:.1f} 分钟")

            self.root.after(0, self.refresh_all_model_lists)
            self.root.after(0, lambda: messagebox.showinfo("训练完成", f"模型训练完成!\n版本: {params['model_version']}\n时间: {training_time / 60:.1f} 分钟"))

        except Exception as e:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()
            self.root.after(0, lambda: messagebox.showerror("训练错误", f"训练失败: {e}"))
        finally:
            self.training_active = False
            self.force_stop_training = False
            # 恢复按钮状态
            self.root.after(0, lambda: self.start_train_btn.config(state="normal"))
            self.root.after(0, lambda: self.stop_train_btn.config(state="disabled"))

    def run_verification_thread(self):
        verify_img = self.verify_img_var.get()
        if not (verify_img and os.path.exists(verify_img)):
            messagebox.showerror("错误", "请选择有效的验证图像")
            return

        selected_indices = self.verify_model_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("错误", "请选择验证模型")
            return

        selected_model_path = self.model_files[selected_indices[0]]
        if not os.path.exists(selected_model_path):
            messagebox.showerror("错误", f"模型文件未找到: {selected_model_path}")
            return

        verify_threshold = self.verify_threshold_var.get()
        if verify_threshold <= 0 or verify_threshold >= 1:
            messagebox.showerror("错误", "验证阈值必须在0和1之间")
            return

        # 确定验证设备
        device_choice = self.verify_device_var.get()
        if device_choice == "auto":
            use_gpu = torch.cuda.is_available()
        elif device_choice == "gpu":
            use_gpu = torch.cuda.is_available()
            if not use_gpu:
                messagebox.showwarning("警告", "GPU不可用，使用CPU进行验证")
        else:
            use_gpu = False

        self.log_text.delete(1.0, tk.END)
        print("🔍 开始验证...")
        print(f"📷 图像: {os.path.basename(verify_img)}")
        print(f"🤖 模型: {os.path.basename(selected_model_path)}")
        print(f"💻 设备: {'GPU' if use_gpu else 'CPU'}")
        print(f"🎯 阈值: {verify_threshold}")

        thread = threading.Thread(target=self.execute_verification_logic, args=(verify_img, selected_model_path, use_gpu, verify_threshold))
        thread.daemon = True
        thread.start()

    def execute_verification_logic(self, verify_img_path, model_path, use_gpu, verify_threshold):
        """执行验证逻辑"""
        device = torch.device("cuda" if torch.cuda.is_available() and use_gpu else "cpu")

        data_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        model = create_model(precision='double')  # 验证时使用双精度
        try:
            checkpoint = torch.load(model_path, map_location=device)
            model.load_state_dict(checkpoint)
            print(f"✅ 模型加载成功: {os.path.basename(model_path)}")
        except Exception as e:
            error_msg = f"❌ 模型加载失败: {e}"
            print(error_msg)
            self.root.after(0, lambda: messagebox.showerror("模型加载错误", error_msg))
            return

        result = verify_image_optimized(model, verify_img_path, data_transform, device=device, threshold=verify_threshold)

        icon = "✅ 真实" if result["is_genuine"] else "❌ 伪造"
        avg_degree_text = f"{result['match_degree']:.2%}"
        result_text = (f"{icon}\n\n"
                      f"平均匹配度: {avg_degree_text}\n"
                      f"模型: {os.path.basename(model_path)}\n"
                      f"阈值: {verify_threshold}\n"
                      f"设备: {device}\n\n"
                      f"详细匹配度 ({len(result['match_degrees'])} 次测试):\n")

        for i, match in enumerate(result["match_degrees"]):
            result_text += f"  测试 {i + 1}: {match:.2%}\n"

        print(f"🎯 验证完成: {result['result']} (匹配度: {avg_degree_text})")
        self.root.after(0, lambda: messagebox.showinfo("验证结果", result_text))

def main():
    # 设置多进程启动方法
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass

    # 设置随机种子
    set_seed()

    # 检查GPU状态
    check_gpu_status()

    root = tk.Tk()
    app = PUFAuthenticationApp(root)
    root.mainloop()

if __name__ == "__main__":
    multiprocessing.freeze_support()
    main()
